timestamp,scenario,architecture,train_buy_count,train_buy_win_rate,train_buy_expectancy,train_sell_count,train_sell_win_rate,train_sell_expectancy,train_total_count,train_total_win_rate,train_total_expectancy,test_buy_count,test_buy_win_rate,test_buy_expectancy,test_sell_count,test_sell_win_rate,test_sell_expectancy,test_total_count,test_total_win_rate,test_total_expectancy,accuracy,auc,f1,precision,recall,threshold,nbars_sl,num_features,hyperparameter_tuning,use_smote,performance_score
2025-10-06 17:31:05,single_model,multi_model,0,0.0,0.0,0,0.0,0.0,9689,0.0,0.0,107604,49.33,2.09,103602,45.15,-52.13,211206,47.28,-24.5,0.1360305501083703,0.6881582198387417,0.1197419823748523,0.0680152750541851,0.5,0.5,5,20,False,False,45.83209277741863
2025-10-06 20:15:37,single_model,multi_model,0,0.0,0.0,0,0.0,0.0,9689,0.0,0.0,107604,49.33,2.09,103602,45.15,-52.13,211206,47.28,-24.5,0.1360305501083703,0.6881582198387417,0.11974198237485237,0.06801527505418516,0.5,0.5,5,20,False,False,45.83209277741863
