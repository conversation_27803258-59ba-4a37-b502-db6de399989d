2025-10-07 09:05:39,022 INFO TRADE_LOG: TEST BUY 100.0 confidence=0.750 horizon=test
2025-10-07 09:05:39,046 INFO 🚀 Enhanced Logging System เริ่มทำงาน
2025-10-07 09:05:39,046 INFO 🏗️ เปิดใช้งาน Financial Analysis System | base_currency=USD | leverage=500
2025-10-07 09:29:31,375 INFO Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-10-07 09:29:31,375 INFO Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-10-07 09:29:31,440 INFO Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-10-07 09:29:31,441 INFO Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-10-07 09:44:09,021 INFO 🚀 Enhanced Logging System เริ่มทำงาน
2025-10-07 10:04:30,053 INFO Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-10-07 10:04:30,066 INFO Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-10-07 10:04:30,126 INFO Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-10-07 10:04:30,127 INFO Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-10-07 10:13:18,576 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=AUDUSD | timeframe=M30 | trades_count=48
2025-10-07 10:13:18,583 INFO 💰 Financial Analysis: AUDUSD_M30 | symbol=AUDUSD | timeframe=M30 | total_trades=48 | total_profit_usd=1854.625840419162 | max_drawdown_usd=337.2542740173744 | margin_per_trade=135.0
2025-10-07 10:13:18,608 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=AUDUSD | timeframe=M60 | trades_count=48
2025-10-07 10:13:18,615 INFO 💰 Financial Analysis: AUDUSD_M60 | symbol=AUDUSD | timeframe=M60 | total_trades=48 | total_profit_usd=1854.625840419162 | max_drawdown_usd=337.2542740173744 | margin_per_trade=135.0
2025-10-07 10:13:18,636 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=EURUSD | timeframe=M30 | trades_count=48
2025-10-07 10:13:18,645 INFO 💰 Financial Analysis: EURUSD_M30 | symbol=EURUSD | timeframe=M30 | total_trades=48 | total_profit_usd=1854.625840419162 | max_drawdown_usd=337.2542740173744 | margin_per_trade=217.0
2025-10-07 10:13:18,669 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=EURUSD | timeframe=M60 | trades_count=48
2025-10-07 10:13:18,670 INFO 💰 Financial Analysis: EURUSD_M60 | symbol=EURUSD | timeframe=M60 | total_trades=48 | total_profit_usd=1854.625840419162 | max_drawdown_usd=337.2542740173744 | margin_per_trade=217.0
2025-10-07 10:13:18,701 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=GBPUSD | timeframe=M30 | trades_count=48
2025-10-07 10:13:18,704 INFO 💰 Financial Analysis: GBPUSD_M30 | symbol=GBPUSD | timeframe=M30 | total_trades=48 | total_profit_usd=1854.625840419162 | max_drawdown_usd=337.2542740173744 | margin_per_trade=252.99999999999997
2025-10-07 10:13:18,733 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=GBPUSD | timeframe=M60 | trades_count=48
2025-10-07 10:13:18,735 INFO 💰 Financial Analysis: GBPUSD_M60 | symbol=GBPUSD | timeframe=M60 | total_trades=48 | total_profit_usd=1854.625840419162 | max_drawdown_usd=337.2542740173744 | margin_per_trade=252.99999999999997
2025-10-07 10:13:18,763 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=GOLD | timeframe=M30 | trades_count=48
2025-10-07 10:13:18,774 INFO 💰 Financial Analysis: GOLD_M30 | symbol=GOLD | timeframe=M30 | total_trades=48 | total_profit_usd=302.19387606287427 | max_drawdown_usd=48.58814110260616 | margin_per_trade=530.0
2025-10-07 10:13:18,802 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=GOLD | timeframe=M60 | trades_count=48
2025-10-07 10:13:18,803 INFO 💰 Financial Analysis: GOLD_M60 | symbol=GOLD | timeframe=M60 | total_trades=48 | total_profit_usd=302.19387606287427 | max_drawdown_usd=48.58814110260616 | margin_per_trade=530.0
2025-10-07 10:13:18,836 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=NZDUSD | timeframe=M30 | trades_count=48
2025-10-07 10:13:18,837 INFO 💰 Financial Analysis: NZDUSD_M30 | symbol=NZDUSD | timeframe=M30 | total_trades=48 | total_profit_usd=1854.625840419162 | max_drawdown_usd=337.2542740173744 | margin_per_trade=123.0
2025-10-07 10:13:18,870 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=NZDUSD | timeframe=M60 | trades_count=48
2025-10-07 10:13:18,877 INFO 💰 Financial Analysis: NZDUSD_M60 | symbol=NZDUSD | timeframe=M60 | total_trades=48 | total_profit_usd=1854.625840419162 | max_drawdown_usd=337.2542740173744 | margin_per_trade=123.0
2025-10-07 10:13:18,901 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=USDCAD | timeframe=M30 | trades_count=48
2025-10-07 10:13:18,902 INFO 💰 Financial Analysis: USDCAD_M30 | symbol=USDCAD | timeframe=M30 | total_trades=48 | total_profit_usd=1368.7275575049166 | max_drawdown_usd=248.89614318625433 | margin_per_trade=200.0
2025-10-07 10:13:18,936 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=USDCAD | timeframe=M60 | trades_count=48
2025-10-07 10:13:18,946 INFO 💰 Financial Analysis: USDCAD_M60 | symbol=USDCAD | timeframe=M60 | total_trades=48 | total_profit_usd=1368.7275575049166 | max_drawdown_usd=248.89614318625433 | margin_per_trade=200.0
2025-10-07 10:13:18,982 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=USDJPY | timeframe=M30 | trades_count=48
2025-10-07 10:13:18,985 INFO 💰 Financial Analysis: USDJPY_M30 | symbol=USDJPY | timeframe=M30 | total_trades=48 | total_profit_usd=1248.************* | max_drawdown_usd=227.************* | margin_per_trade=200.0
2025-10-07 10:13:19,019 INFO 🏗️ เปิดใช้งาน process_trade_cycle | symbol=USDJPY | timeframe=M60 | trades_count=48
2025-10-07 10:13:19,027 INFO 💰 Financial Analysis: USDJPY_M60 | symbol=USDJPY | timeframe=M60 | total_trades=48 | total_profit_usd=1248.************* | max_drawdown_usd=227.************* | margin_per_trade=200.0
2025-10-07 10:13:19,035 INFO 🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์ | account_balance=1000
2025-10-07 10:13:25,912 INFO ✅ การวิเคราะห์ทางการเงินเสร็จสมบูรณ์ | account_balance=1000 | total_trades=672 | total_profit_usd=20676.************ | max_drawdown_usd=3747.************* | recommended_lot_size=0.005337293931107849 | max_risk_percentage=2.0
