
# 🚀 LightGBM Trading System Runbook

## 📋 ลำดับการสั่งงาน (Quick Start)

### 1. เตรียมข้อมูล
```python
# ตรวจสอบข้อมูล CSV
python LightGBM_11_5.py --check-data

# แปลงข้อมูล (ถ้าจำเป็น)
python fix_all_csv_files.py
```

### 2. การตั้งค่าระบบ
```python
# บันทึกการตั้งค่าปัจจุบัน
save_current_configuration()

# ตรวจสอบการตั้งค่า
print(f"HORIZONS: {HORIZONS}")
print(f"USE_NEXT_CLOSE_SYSTEM: {USE_NEXT_CLOSE_SYSTEM}")
print(f"USE_MULTI_HORIZON_DECISION: {USE_MULTI_HORIZON_DECISION}")
```

### 3. การเทรนโมเดล
```python
# Development Mode (เทรนโมเดลใหม่)
DEVELOPMENT_MODE = True
TRAIN_NEW_MODEL = True

# Production Mode (ใช้โมเดลเดิม)
DEVELOPMENT_MODE = False
TRAIN_NEW_MODEL = False
```

### 4. การทดสอบ
```python
# ทดสอบ Next Close System
if USE_NEXT_CLOSE_SYSTEM:
    # ทดสอบ optimal threshold
    threshold_results = find_optimal_threshold_next_close(trade_df, symbol, timeframe, HORIZONS)

    # ทดสอบ optimal nBars_SL
    nbars_results = find_optimal_nbars_sl_next_close(trade_df, symbol, timeframe, HORIZONS)
```

### 5. การ Monitor
```python
# ตรวจสอบ trade logs
analyze_trade_performance("LightGBM/trade_log.txt")

# ตรวจสอบ system logs
tail -f LightGBM/system.log
```

## 🔧 การตั้งค่าสำคัญ

### HORIZONS Configuration
- เดิม: [5, 10, 15]
- ใหม่: [3, 5, 10, 15] (เพิ่ม horizon=3 สำหรับการตัดสินใจระยะสั้นมาก)

### Logging System
- System Log: LightGBM/system.log
- Trade Log (JSON): LightGBM/trade_log.txt
- Trade Log (Human): LightGBM/trade_log_human.txt

### Decision Flow
1. Load market data
2. Preprocess & compute features
3. For each horizon in HORIZONS:
   - Model prediction
   - Confidence calculation
4. Apply decision rule (2of3, all3, majority)
5. Risk checks
6. Execute order
7. Log trade

## 📊 การตรวจสอบผลลัพธ์

### Trade Performance
```python
# อ่าน trade logs
df = read_trade_logs()
print(df.head())

# วิเคราะห์ประสิทธิภาพ
stats = analyze_trade_performance()
```

### Model Performance
```python
# ตรวจสอบ model quality
print(f"Protection Mode: {PROTECTION_MODE}")
print(f"Model Quality Thresholds: {MODEL_QUALITY_THRESHOLDS}")
```

## ⚠️ Troubleshooting

### ปัญหาทั่วไป
1. ข้อมูลไม่เพียงพอ → ปรับ MIN_TOTAL_TRADES
2. Model ไม่ผ่านเกณฑ์ → ปรับ PROTECTION_MODE
3. Threshold ไม่เหมาะสม → รัน find_optimal_threshold_next_close
4. Log ไม่ทำงาน → ตรวจสอบ permissions ของโฟลเดอร์

### การ Debug
```bash
# ตรวจสอบ log files
ls -la LightGBM/*.log LightGBM/*.txt

# ตรวจสอบ trade logs
python -c "from LightGBM_11_5 import analyze_trade_performance; analyze_trade_performance()"

# ตรวจสอบ system logs
grep "ERROR" LightGBM/system.log
```
