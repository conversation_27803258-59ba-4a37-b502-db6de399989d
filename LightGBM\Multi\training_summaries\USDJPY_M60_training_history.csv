timestamp,scenario,architecture,train_buy_count,train_buy_win_rate,train_buy_expectancy,train_sell_count,train_sell_win_rate,train_sell_expectancy,train_total_count,train_total_win_rate,train_total_expectancy,test_buy_count,test_buy_win_rate,test_buy_expectancy,test_sell_count,test_sell_win_rate,test_sell_expectancy,test_total_count,test_total_win_rate,test_total_expectancy,accuracy,auc,f1,precision,recall,threshold,nbars_sl,num_features,hyperparameter_tuning,use_smote,performance_score
2025-10-06 19:57:11,single_model,multi_model,0,0.0,0.0,0,0.0,0.0,10226,0.0,0.0,113754,49.14,-10.32,107274,44.38,-23.64,221028,46.83,-16.79,0.2513201642871113,0.6003115737408975,0.2008440137542982,0.1256600821435556,0.5,0.5,5,20,False,False,46.605443598882
2025-10-06 21:46:33,single_model,multi_model,0,0.0,0.0,0,0.0,0.0,10226,0.0,0.0,113754,49.14,-10.32,107274,44.38,-23.64,221028,46.83,-16.79,0.2513201642871113,0.6003115737408975,0.20084401375429822,0.12566008214355565,0.5,0.5,5,20,False,False,46.605443598882
